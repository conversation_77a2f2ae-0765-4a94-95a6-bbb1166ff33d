package click

import (
	"bytes"
	"context"
	"crypto/sha1"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

const (
	defaultTimeout     = 30 * time.Second
	holdCreateEndpoint = "/v2/merchant/hold/create"
)

type Client struct {
	MerchantID string
	ServiceID  string
	SecretKey  string
	httpClient *http.Client
}

func NewClient(merchantID, serviceID, secretKey string) *Client {
	return &Client{
		MerchantID: merchantID,
		ServiceID:  serviceID,
		SecretKey:  secretKey,
		httpClient: &http.Client{Timeout: defaultTimeout},
	}
}

func (c *Client) generateAuthHeader() string {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	digestInput := timestamp + c.SecretKey

	// Create SHA1 hash
	hash := sha1.New()
	hash.Write([]byte(digestInput))
	digest := fmt.Sprintf("%x", hash.Sum(nil))

	return fmt.Sprintf("%s:%s:%s", c.<PERSON>, digest, timestamp)
}

func (c *Client) sendRequest(ctx context.Context, endpoint string, method string, body any) ([]byte, error) {
	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("marshal request body: %v", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}
	fullURL := "https://api.click.uz" + endpoint

	req, err := http.NewRequestWithContext(ctx, method, fullURL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Auth", c.generateAuthHeader())

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("execute request: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

func (c *Client) CreateHoldWithContext(ctx context.Context, phoneNumber, externalID string, amount, timeSeconds int) (*HoldCreateResponse, error) {
	serviceID, err := strconv.Atoi(c.ServiceID)
	if err != nil {
		return nil, fmt.Errorf("invalid service_id: %v", err)
	}

	request := HoldCreateRequest{
		ServiceID:   serviceID,
		PhoneNumber: phoneNumber,
		Amount:      amount,
		ExternalID:  externalID,
		Time:        timeSeconds,
	}

	respBody, err := c.sendRequest(ctx, holdCreateEndpoint, http.MethodPost, request)
	if err != nil {
		return nil, fmt.Errorf("hold create request: %v", err)
	}

	var response HoldCreateResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("parse hold create response: %v", err)
	}

	return &response, nil
}

func (c *Client) GenerateSuperAppPayment(amount, transactionID int, auth bool, hideCross bool, returnPage string) string {

	deepLink := fmt.Sprintf("https://my.click.uz/app/webView?auth=%t&hide_cross=%t&url=", auth, hideCross)

	encodedDeepLinkWithPage := deepLink + url.QueryEscape(returnPage)

	encodedFinalDeepLink := url.QueryEscape(encodedDeepLinkWithPage)

	// Create the final URL
	finalURL := fmt.Sprintf(
		"https://my.click.uz/services/pay/?service_id=%s&merchant_id=%s&amount=%d&transaction_param=%d&return_url=%s",
		c.ServiceID,
		c.MerchantID,
		amount,
		transactionID,
		encodedFinalDeepLink,
	)

	return finalURL
}
