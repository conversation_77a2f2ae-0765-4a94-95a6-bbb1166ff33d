package click

import "fmt"

type ClickError struct {
	Status  int
	Message string
}

func (e *ClickError) Error() string {
	return fmt.Sprintf("Click API error (status: %d): %s", e.Status, e.Message)
}

func NewClickError(status int, message string) error {
	return &ClickError{
		Status:  status,
		Message: message,
	}
}

func IsClickError(err error) bool {
	_, ok := err.(*ClickError)
	return ok
}

func GetClickErrorStatus(err error) int {
	if clickErr, ok := err.(*ClickError); ok {
		return clickErr.Status
	}
	return 0
}

func GetClickErrorMessage(err error) string {
	if clickErr, ok := err.(*ClickError); ok {
		return clickErr.Message
	}
	return ""
}
