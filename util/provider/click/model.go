package click

type HoldCreateRequest struct {
	ServiceID   int    `json:"service_id"`
	PhoneNumber string `json:"phone_number"`
	Amount      int    `json:"amount"`
	ExternalID  string `json:"external_id"`
	Time        int    `json:"time"`
}

type HoldCreateResponse struct {
	Status          int     `json:"status"`
	Message         string  `json:"message"`
	HoldID          string  `json:"hold_id"`
	PaymentID       int64   `json:"payment_id"`
	FullAmount      int     `json:"full_amount"`
	ConfirmedAmount int     `json:"confirmed_amount"`
	CancelledAmount int     `json:"cancelled_amount"`
	CreatedTime     string  `json:"created_time"`
	CompletedTime   *string `json:"completed_time"`
}

func (r *HoldCreateResponse) IsSuccess() bool {
	return r.Status == 1
}

func (r *HoldCreateResponse) ToError() error {
	if r.IsSuccess() {
		return nil
	}
	return NewClickError(r.Status, r.Message)
}
