package config

import (
	"fmt"
	"sync"

	"github.com/ilyakaznacheev/cleanenv"
)

var (
	cfg  *Config
	once sync.Once
)

type Mysql struct {
	Host string `env:"M_DB_HOST" env-required:"true"`
	Port string `env:"M_DB_PORT" env-required:"true"`
	Name string `env:"M_DB_NAME" env-required:"true"`
	User string `env:"M_DB_USER" env-required:"true"`
	Pass string `env:"M_DB_PASS" env-required:"true"`
}

type PaymentPostgres struct {
	Host string `env:"P_DB_HOST" env-required:"true"`
	Name string `env:"P_DB_NAME" env-required:"true"`
	User string `env:"P_DB_USER" env-required:"true"`
	Pass string `env:"P_DB_PASS" env-required:"true"`
	Port string `env:"P_DB_PORT" env-required:"true"`
}

type BillingPostgres struct {
	Host string `env:"P_2_DB_HOST" env-required:"true"`
	Name string `env:"P_2_DB_NAME" env-required:"true"`
	User string `env:"P_2_DB_USER" env-required:"true"`
	Pass string `env:"P_2_DB_PASS" env-required:"true"`
	Port string `env:"P_2_DB_PORT" env-required:"true"`
}

// GetDSN  need to make dsn connection string
func (db *Mysql) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%v)/%s?parseTime=true&loc=%s", db.User, db.Pass, db.Host, db.Port, db.Name, "Asia%2FTashkent")
}

// GetDSN  need to make dsn connection string
func (db *PaymentPostgres) GetDSN() string {
	return fmt.Sprintf("host=%s port=%v user=%s password=%s dbname=%s sslmode=disable TimeZone=Asia/Tashkent", db.Host, db.Port, db.User, db.Pass, db.Name)
}

// GetDSN  need to make dsn connection string
func (db *BillingPostgres) GetDSN() string {
	return fmt.Sprintf("host=%s port=%v user=%s password=%s dbname=%s sslmode=disable TimeZone=Asia/Tashkent", db.Host, db.Port, db.User, db.Pass, db.Name)
}

type Config struct {
	Host string `env:"HOST" env-required:"true"`

	FernetKey string `env:"FERNET_KEY"`
	SecretKey string `env:"SECRET_KEY"`

	Payme struct {
		Url         string `env:"PAYCOM_URL"`
		Id          string `env:"PAYCOM_ID"`
		Key         string `env:"PAYCOM_KEY"`
		DriverKey   string `env:"PAYCOM_DRIVER_KEY"`
		FallBackUrl string `env:"PAYCOM_FALLBACK_URL"`
	}

	Paynet struct {
		Username    string `env:"PAYNET_USERNAME"`
		Password    string `env:"PAYNET_PASSWORD"`
		FallBackUrl string `env:"PAYNET_FALLBACK_URL"`
	}

	Ubk struct {
		Url           string `env:"UNIVERSAL_BANK_URL"`
		Token         string `env:"UNIVERSAL_BANK_TOKEN"`
		AccountNumber string `env:"UNIVERSAL_BANK_ACCOUNT_NUMBER"`
		Username      string `env:"UNIVERSAL_BANK_USERNAME"`
		Password      string `env:"UNIVERSAL_BANK_PASSWORD"`
		SecretKey     string `env:"UNIVERSAL_BANK_SECRET"`
	}

	Atmos struct {
		Url          string `env:"ATMOS_URL"`
		ClientID     string `env:"ATMOS_CLIENT_ID"`
		ClientSecret string `env:"ATMOS_CLIENT_SECRET"`
		StoreID      int    `env:"ATMOS_STORE_ID"`
		ApiKey       string `env:"ATMOS_KEY"`
	}

	UbkA2A struct {
		Url      string `env:"UNIVERSAL_BANK_A2A_URL"`
		TaxId    string `env:"UNIVERSAL_BANK_A2A_TAX_ID"`
		BankCode string `env:"UNIVERSAL_BANK_A2A_BANK_CODE"`
		Account  string `env:"UNIVERSAL_BANK_A2A_ACCOUNT"`
		Username string `env:"UNIVERSAL_BANK_A2A_USERNAME"`
		Password string `env:"UNIVERSAL_BANK_A2A_PASSWORD"`
	}

	Defaults struct {
		P2PRetriesToCancelWithDebt                 int `env:"P2P_RETRIES_TO_CANCEL_WITH_DEBT" env-default:"40"`
		BillingPaycomAccountNum                    int `env:"BILLING_PAYCOM_ACCOUNT_NUM"`
		BillingDeductionAccountNum                 int `env:"BILLING_DEDUCTION_ACCOUNT_NUM"`
		PaymePaymentTypeFromMytaxiOutsourceBilling int `env:"PAYME_PAYMENT_TYPE_FROM_MYTAXI_OUTSOURCE_BILLING" env-default:"7"`
		SysParamsPollInterval                      int `env:"SYS_PARAMS_POLL_INTERVAL" env-default:"300"`
	}

	Mysql           Mysql
	PaymentPostgres PaymentPostgres
	BillingPostgres BillingPostgres

	Service struct {
		BillingClick struct {
			Host string `env:"BILLING_CLICK_URL"`
			User string `env:"BILLING_CLICK_USER"`
			Pass string `env:"BILLING_CLICK_PASS"`
		}
		CorpService struct {
			Host string `env:"CORP_SERVICE_URL"`
		}
	}

	Nats struct {
		Host  string `env:"NATS_HOST" env-required:"true"`
		Token string `env:"NATS_TOKEN"`
	}

	Redis struct {
		Host     string `env:"REDIS_HOST" env-required:"true"`
		Password string `env:"REDIS_PASSWORD"`
		DB       int    `env:"REDIS_DB"`
	}
	Click struct {
		MerchantID     string `env:"CLICK_MERCHANT_ID"`
		ServiceID      string `env:"CLICK_SERVICE_ID"`
		SecretKey      string `env:"CLICK_SECRET_KEY"`
		MerchantUserId string `env:"CLICK_MERCHANT_USER_ID"`
	}

	LogLevel string `env:"LOG_LEVEL" env-default:"info"`
}

func Get() *Config {
	once.Do(func() {
		cfg = new(Config)
		err := cleanenv.ReadEnv(cfg)
		if err != nil {
			panic(err)
		}
	})
	return cfg
}
