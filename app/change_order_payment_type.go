package app

import (
	"context"
	"fmt"

	"billing_service/model"
)

func (a *App) ChangeOrderPaymentType(ctx context.Context, orderId int, paymentTypeId int8) (errType string, err error) {
	// Get order information to validate and get client/driver IDs
	order, err := a.repo.GetOrderInfo(ctx, orderId)
	if err != nil {
		err = fmt.Errorf("error change payment type. order_id: %d %v", orderId, err)
		return
	}

	if order.ClientId == 0 {
		errType = "order_not_found"
		err = fmt.Errorf("order %d not found", orderId)
		return
	}

	// Note: Since this is billing service, we don't have direct access to order status
	// The order status validation should be done by the calling service
	// For now, we'll proceed with the payment type change

	// Change the payment type in database
	err = a.repo.ChangeOrderPaymentType(ctx, orderId, paymentTypeId)
	if err != nil {
		err = fmt.Errorf("change order %d payment type: %v", orderId, err)
		return
	}

	// Publish order status event to message broker
	event := model.OrderStatusEvent{
		OrderId:  orderId,
		ClientId: order.ClientId,
		DriverId: 0, // We don't have driver ID in OrderInfo from billing service
		Status:   model.ChangedPaymentType,
	}

	msgId := fmt.Sprintf("order:%d:payment_type_changed", orderId)

	err = a.nats.Publish("orders.payment_type_changed", msgId, event)
	if err != nil {
		err = fmt.Errorf("publish order %d status to message broker: %v", orderId, err)
		return
	}

	a.log.Infof("Order %d payment type changed to %d", orderId, paymentTypeId)

	return
}
