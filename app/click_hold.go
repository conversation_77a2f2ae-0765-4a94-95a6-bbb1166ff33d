package app

import (
	"context"
	"fmt"
	"strconv"

	"billing_service/model"

	"github.com/google/uuid"
	null "github.com/guregu/null/v6"
)

func (a *App) CreateClickHold(ctx context.Context, req model.CreateClickHoldRequest) (resp model.CreateClickHoldResponse, err error) {
	order, err := a.repo.GetOrderInfo(ctx, req.OrderId)
	if err != nil {
		err = fmt.Errorf("get order info: %v", err)
		return
	}

	if order.ClientId == 0 {
		err = fmt.Errorf("order not found")
		return
	}

	timeMinutes := req.TimeMinutes
	if timeMinutes <= 0 {
		timeMinutes = 60
	}

	externalID := fmt.Sprintf("order_%d_%s", req.OrderId, uuid.NewString()[:8])

	holdResponse, err := a.click.CreateHoldWithContext(ctx, req.PhoneNumber, externalID, req.Amount, timeMinutes*60)
	if err != nil {
		a.log.Warnf("Click hold creation failed for order %d: %v", req.OrderId, err)

		err = a.updateOrderPaymentTypeToCash(ctx, req.OrderId)
		if err != nil {
			err = fmt.Errorf("failed to update payment type to cash: %v", err)
			return
		}

		resp = model.CreateClickHoldResponse{
			Status:      "success",
			Message:     "Hold creation failed, payment type changed to cash",
			PaymentType: "cash",
		}
		return
	}

	if !holdResponse.IsSuccess() {
		a.log.Warnf("Click hold creation unsuccessful for order %d: status %d, message: %s",
			req.OrderId, holdResponse.Status, holdResponse.Message)

		// Hold creation failed, fallback to cash payment
		err = a.updateOrderPaymentTypeToCash(ctx, req.OrderId)
		if err != nil {
			err = fmt.Errorf("failed to update payment type to cash: %v", err)
			return
		}

		resp = model.CreateClickHoldResponse{
			Status:      "success",
			Message:     fmt.Sprintf("Hold creation failed (%s), payment type changed to cash", holdResponse.Message),
			PaymentType: "cash",
		}
		return
	}

	payment := model.Payment{
		UserId:     order.ClientId,
		UserType:   "client",
		OrderId:    null.IntFrom(int64(req.OrderId)),
		Amount:     req.Amount,
		Status:     model.PaymentStatusCreated,
		Reason:     model.PaymentReasonPayOrder,
		ProviderId: model.ProviderIdClick,
		Invoice:    strconv.FormatInt(holdResponse.PaymentID, 10),
	}

	err = a.repo.CreatePayment(ctx, payment)
	if err != nil {
		a.log.Errorf("Failed to create payment record for order %d: %v", req.OrderId, err)

		_, cancelErr := a.click.CancelHoldWithContext(ctx, holdResponse.PaymentID)
		if cancelErr != nil {
			a.log.Errorf("Failed to cancel hold %d after payment creation failure: %v", holdResponse.PaymentID, cancelErr)
		}

		err = a.updateOrderPaymentTypeToCash(ctx, req.OrderId)
		if err != nil {
			err = fmt.Errorf("failed to update payment type to cash after payment creation failure: %v", err)
			return
		}

		resp = model.CreateClickHoldResponse{
			Status:      "success",
			Message:     "Payment record creation failed, payment type changed to cash",
			PaymentType: "cash",
		}
		return
	}

	createdPayment, err := a.repo.GetPaymentByInvoice(ctx, payment.Invoice)
	if err != nil {
		a.log.Errorf("Failed to get created payment for order %d: %v", req.OrderId, err)
	}

	resp = model.CreateClickHoldResponse{
		Status:      "success",
		Message:     "Hold created successfully",
		PaymentType: "click",
		HoldID:      holdResponse.HoldID,
		PaymentID:   holdResponse.PaymentID,
		PaymentDBID: createdPayment.Id,
		FullAmount:  holdResponse.FullAmount,
		CreatedTime: holdResponse.CreatedTime,
	}

	a.log.Infof("Click hold created successfully for order %d: hold_id=%s, payment_id=%d",
		req.OrderId, holdResponse.HoldID, holdResponse.PaymentID)

	return
}

func (a *App) updateOrderPaymentTypeToCash(ctx context.Context, orderID int) error {
	// Update the order payment details to use cash payment type
	// This assumes there's a method to update payment type - you may need to implement this
	// For now, we'll create/update the order payment details with cash payment type

	// Get order info to get client and driver IDs
	order, err := a.repo.GetOrderInfo(ctx, orderID)
	if err != nil {
		return fmt.Errorf("get order info for cash update: %v", err)
	}

	// Update or create order payment details with cash payment type
	err = a.repo.CreateOrderPaymentDetails(ctx, orderID, 0, order.ClientId, 0, model.PaymentTypeCash)
	if err != nil {
		return fmt.Errorf("update order payment details to cash: %v", err)
	}

	a.log.Infof("Order %d payment type updated to cash", orderID)
	return nil
}
