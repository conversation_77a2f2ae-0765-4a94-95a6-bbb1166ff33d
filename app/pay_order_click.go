package app

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"billing_service/model"

	null "github.com/guregu/null/v6"
	"github.com/riverqueue/river"
)

func (a *App) PayOrderClickTask(ctx context.Context, req model.PayOrderRequest, attempt int) (err error) {
	// Check if payment already finished
	payment, err := a.repo.GetPaymentStatusByOrderId(ctx, req.OrderId, model.PaymentReasonPayOrder)
	if err != nil {
		return fmt.Errorf("get payment status: %v", err)
	}

	if payment.IsFinished() {
		a.log.Infof("Order %d payment already finished", req.OrderId)
		return nil
	}

	// Get existing hold payment record
	holdPayment, err := a.repo.GetPaymentByOrderIdAndProvider(ctx, req.OrderId, model.ProviderIdClick, model.PaymentReasonPayOrder)
	if err != nil {
		return fmt.Errorf("get hold payment: %v", err)
	}

	if holdPayment.Id == 0 {
		return fmt.Errorf("no Click hold found for order %d", req.OrderId)
	}

	// Check if amounts match
	if holdPayment.Amount != req.TotalClientCost {
		a.log.Warnf("Amount mismatch for order %d: hold=%d, order=%d",
			req.OrderId, holdPayment.Amount, req.TotalClientCost)

		// Cancel old hold and create new one
		err = a.handleAmountMismatch(ctx, req, holdPayment)
		if err != nil {
			return fmt.Errorf("handle amount mismatch: %v", err)
		}

		// Retry with new hold
		return river.JobSnooze(30 * time.Second)
	}

	// Get hold status
	paymentID, err := strconv.ParseInt(holdPayment.Invoice, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid payment ID: %v", err)
	}

	statusResp, err := a.click.GetHoldStatusWithContext(ctx, paymentID)
	if err != nil {
		return fmt.Errorf("get hold status: %v", err)
	}

	// Check hold status
	switch {
	case statusResp.IsConfirmed():
		// Already confirmed, update payment status
		err = a.repo.UpdatePaymentStatus(ctx, holdPayment.Invoice, model.PaymentStatusFinished)
		if err != nil {
			return fmt.Errorf("update payment status: %v", err)
		}

		err = a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
		if err != nil {
			return fmt.Errorf("update order payment status: %v", err)
		}

		a.log.Infof("Order %d Click payment already confirmed", req.OrderId)
		return nil

	case statusResp.IsCancelled() || statusResp.IsExpired() || statusResp.IsFailed():
		// Hold failed, fallback to cash
		a.log.Warnf("Hold failed for order %d: status=%d", req.OrderId, statusResp.Status)

		err = a.updateOrderPaymentTypeToCash(ctx, req.OrderId)
		if err != nil {
			return fmt.Errorf("fallback to cash: %v", err)
		}
		return nil

	case statusResp.IsHeld() || statusResp.IsPending():
		// Confirm the hold
		confirmResp, err := a.click.ConfirmHoldWithContext(ctx, paymentID, req.TotalClientCost)
		if err != nil {
			if attempt >= 50 {
				// Max retries reached, create debt
				return a.createClientDebt(ctx, req, true)
			}
			return fmt.Errorf("confirm hold: %v", err)
		}

		if !confirmResp.IsSuccess() {
			if attempt >= 50 {
				// Max retries reached, create debt
				return a.createClientDebt(ctx, req, true)
			}
			return fmt.Errorf("hold confirmation failed: %s", confirmResp.Message)
		}

		// Update payment status
		err = a.repo.UpdatePaymentStatus(ctx, holdPayment.Invoice, model.PaymentStatusFinished)
		if err != nil {
			return fmt.Errorf("update payment status: %v", err)
		}

		err = a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
		if err != nil {
			return fmt.Errorf("update order payment status: %v", err)
		}

		a.log.Infof("Order %d Click payment confirmed successfully", req.OrderId)
		return nil

	default:
		return fmt.Errorf("unknown hold status: %d", statusResp.Status)
	}
}

func (a *App) handleAmountMismatch(ctx context.Context, req model.PayOrderRequest, oldPayment model.Payment) error {
	// Cancel old hold
	paymentID, err := strconv.ParseInt(oldPayment.Invoice, 10, 64)
	if err != nil {
		a.log.Errorf("Invalid payment ID for cancellation: %v", err)
	} else {
		_, cancelErr := a.click.CancelHoldWithContext(ctx, paymentID)
		if cancelErr != nil {
			a.log.Errorf("Failed to cancel old hold %d: %v", paymentID, cancelErr)
		}
	}

	// Update old payment status
	err = a.repo.UpdatePaymentStatus(ctx, oldPayment.Invoice, model.PaymentStatusCancelled)
	if err != nil {
		a.log.Errorf("Failed to update old payment status: %v", err)
	}

	// Get order info for new hold
	order, err := a.repo.GetOrderInfo(ctx, req.OrderId)
	if err != nil {
		return fmt.Errorf("get order info: %v", err)
	}

	// Get client phone
	phoneNumber, err := a.repo.GetClientPhoneById(ctx, order.ClientId)
	if err != nil {
		return fmt.Errorf("get client phone: %v", err)
	}

	// Create new hold with correct amount
	externalID := fmt.Sprintf("order_%d_retry_%d", req.OrderId, time.Now().Unix())
	holdResponse, err := a.click.CreateHoldWithContext(ctx, phoneNumber, externalID, req.TotalClientCost, 60*60)
	if err != nil {
		return fmt.Errorf("create new hold: %v", err)
	}

	if !holdResponse.IsSuccess() {
		return fmt.Errorf("new hold creation failed: %s", holdResponse.Message)
	}

	// Create new payment record
	newPayment := model.Payment{
		UserId:     order.ClientId,
		UserType:   "client",
		OrderId:    null.IntFrom(int64(req.OrderId)),
		Amount:     req.TotalClientCost,
		Status:     model.PaymentStatusCreated,
		Reason:     model.PaymentReasonPayOrder,
		ProviderId: model.ProviderIdClick,
		Invoice:    strconv.FormatInt(holdResponse.PaymentID, 10),
	}

	err = a.repo.CreatePayment(ctx, newPayment)
	if err != nil {
		// Cancel the new hold if payment creation fails
		_, cancelErr := a.click.CancelHoldWithContext(ctx, holdResponse.PaymentID)
		if cancelErr != nil {
			a.log.Errorf("Failed to cancel new hold after payment creation failure: %v", cancelErr)
		}
		return fmt.Errorf("create new payment: %v", err)
	}

	a.log.Infof("Created new hold for order %d with correct amount %d", req.OrderId, req.TotalClientCost)
	return nil
}

func (a *App) createClientDebt(ctx context.Context, req model.PayOrderRequest, payToDriver bool) error {
	// Create debt record
	err := a.repo.CreateClientDebt(ctx, req.ClientId, req.OrderId, req.TotalClientCost, "Click payment failed after max retries")
	if err != nil {
		return fmt.Errorf("create client debt: %v", err)
	}

	// Send notifications
	msgId := fmt.Sprintf("order:%d:debt", req.OrderId)
	msg := fmt.Sprintf(model.MessageToOperClientDebt, req.OrderId, req.TotalClientCost, req.ClientId, req.DriverId)

	a.repo.SendOperatorNotification("client_debts", msgId, msg)
	a.repo.SendClientNotification(req.ClientId, req.OrderId, req.TotalClientCost, msgId, "client_debt_create")

	// Pay driver if needed
	if payToDriver {
		r := model.A2CPaymentRequest{
			OrderId:  req.OrderId,
			DriverId: req.DriverId,
			Amount:   req.TotalClientCost,
			Reason:   model.PaymentReasonPayOrderForDriver,
			Comment:  req.Comment,
		}
		_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: r}, nil)
		if err != nil {
			return fmt.Errorf("schedule driver payment: %v", err)
		}
	}

	a.log.Infof("Created debt for order %d: %d tiyin", req.OrderId, req.TotalClientCost)
	return nil
}
