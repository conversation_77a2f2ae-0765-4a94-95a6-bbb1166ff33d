package app

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"billing_service/model"
	"billing_service/util/provider/ubk_a2a"
)

const (
	minimalPaymeTransferAmount = 1000
)

func (a *App) PayOrder(req model.PayOrderRequest) (err error) {
	r, _ := json.Marshal(&req)
	a.log.Infof("pay order %d started: %s", req.OrderId, r)

	err = req.CheckRequest()
	if err != nil {
		return
	}

	ctx, cancel := context.WithTimeout(a.ctx, 1*time.Minute)
	defer cancel()

	err = a.repo.CreateOrderPaymentDetails(ctx, req.OrderId, req.CorpId, req.ClientId, req.DriverId, req.PaymentTypeId)
	if err != nil {
		return
	}

	order, err := a.repo.GetOrderInfo(ctx, req.OrderId)
	if err != nil {
		return
	}

	// send payment to billing

	driverBalance, err := a.billingPayOrder(ctx, req, order)
	if err != nil {
		return
	}

	req.PayToDriverCard = true
	req.CardId = int(order.ClientCardId.Int64)
	req.Comment = "Оплата за заказ " + strconv.Itoa(req.OrderId)

	// pay cashback cost to driver balance

	if req.CashbackCost > 0 {
		req.TotalCost -= req.CashbackCost
		msgId := fmt.Sprintf("refill_driver_balance_for_order_cashback:%d", req.OrderId)
		a.repo.SendDriverNotification(req.DriverId, req.OrderId, req.CashbackCost, msgId, "driver_order_paid_to_balance")
	}

	switch req.PaymentTypeId {

	case model.PaymentTypeCorp:
		err = a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
		if err != nil {
			return
		}

		req.PayToDriverCard, err = a.isPayToDriver(ctx, req)
		if err != nil {
			return
		}

		if req.PayToDriverCard {
			r := model.A2CPaymentRequest{
				OrderId:  req.OrderId,
				DriverId: req.DriverId,
				Amount:   req.TotalCost,
				Reason:   model.PaymentReasonPayOrderForDriver,
				Comment:  req.Comment,
			}
			_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: r}, nil)
			if err != nil {
				return
			}
		}

	case model.PaymentTypePayme:
		req.PayToDriverCard, err = a.isPayToDriver(ctx, req)
		if err != nil {
			return
		}

		if req.TotalClientCost > 0 {
			_, err = a.river.Insert(ctx, PayOrderPaymeArgs{PayOrderRequest: req}, nil)
			if err != nil {
				return
			}
		} else {
			err = a.repo.UpdateOrderPaymentStatusToSuccess(ctx, req.OrderId)
			if err != nil {
				return
			}
		}

		cost := req.TariffDiscountCost + req.PromoCost
		if cost > 0 {
			r := model.A2CPaymentRequest{
				OrderId:  req.OrderId,
				DriverId: req.DriverId,
				Amount:   cost,
				Reason:   model.PaymentReasonPayOrderDiscountForDriver,
				Comment:  req.Comment,
			}
			_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: r}, nil)
			if err != nil {
				return
			}
		}

	case model.PaymentTypeCash:
		cost := req.TariffDiscountCost + req.PromoCost
		if cost > 0 {
			r := model.A2CPaymentRequest{
				OrderId:  req.OrderId,
				DriverId: req.DriverId,
				Amount:   cost,
				Reason:   model.PaymentReasonPayOrderDiscountForDriver,
				Comment:  req.Comment,
			}
			_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: r}, nil)
			if err != nil {
				return
			}
		}

		err = a.repo.UpdateOrderPaymentStatusToSuccess(ctx, req.OrderId)
		if err != nil {
			return
		}

	case model.PaymentTypeAtmos:
		_, err = a.river.Insert(ctx, PayOrderAtmosArgs{PayOrderRequest: req}, nil)
		if err != nil {
			return
		}

		req.PayToDriverCard, err = a.isPayToDriver(ctx, req)
		if err != nil {
			return
		}

		if req.PayToDriverCard {
			r := model.A2CPaymentRequest{
				OrderId:  req.OrderId,
				DriverId: req.DriverId,
				Amount:   req.TotalCost,
				Reason:   model.PaymentReasonPayOrderForDriver,
				Comment:  req.Comment,
			}
			_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: r}, nil)
			if err != nil {
				return
			}
		}

	case model.PaymentTypeClick:
		req.PayToDriverCard, err = a.isPayToDriver(ctx, req)
		if err != nil {
			return
		}

		if req.TotalClientCost > 0 {
			_, err = a.river.Insert(ctx, PayOrderClickArgs{PayOrderRequest: req}, nil)
			if err != nil {
				return
			}
		} else {
			err = a.repo.UpdateOrderPaymentStatusToSuccess(ctx, req.OrderId)
			if err != nil {
				return
			}
		}

		// Pay driver for discounts and promos
		cost := req.TariffDiscountCost + req.PromoCost
		if cost > 0 {
			r := model.A2CPaymentRequest{
				OrderId:  req.OrderId,
				DriverId: req.DriverId,
				Amount:   cost,
				Reason:   model.PaymentReasonPayOrderDiscountForDriver,
				Comment:  req.Comment,
			}
			_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: r}, nil)
			if err != nil {
				return
			}
		}

	default:
		err = fmt.Errorf("unknown payment type: %d", req.PaymentTypeId)
		return
	}

	// refill driver balance from driver card for minimal limit

	mimimalDriverBalanceForAutorefill := a.repo.GetSysParam("driver_min_balance_for_autorefill", "1000").Int()

	if driverBalance < mimimalDriverBalanceForAutorefill {
		toBalance := mimimalDriverBalanceForAutorefill - driverBalance

		if toBalance >= minimalPaymeTransferAmount {
			r := model.RefillDriverBalanceRequest{
				OrderId:  req.OrderId,
				DriverId: req.DriverId,
				Amount:   toBalance,
				Balance:  driverBalance,
			}
			_, err = a.river.Insert(ctx, RefillDriverBalanceArgs{RefillDriverBalanceRequest: r}, nil)
			if err != nil {
				a.log.Errorf("insert driver %d balance refill job: %v", req.DriverId, err)
				err = nil
			}
		}
	}

	// save partner a2a transaction

	if req.PartnerCommission > 0 && a.repo.GetSysParam("a2a_enabled", "").Bool() {
		var partner model.Partner
		partner, err = a.repo.GetDriverPartner(ctx, req.DriverId)
		if err != nil {
			return
		}
		if partner.Id > 0 {
			var err error
			t := model.A2ATransaction{
				OrderId:      req.OrderId,
				PartnerId:    partner.Id,
				Amount:       int(float32(req.TotalCost) / 100 * req.PartnerCommission),
				OrderAmount:  req.TotalCost,
				ProviderType: 4,
				Commission:   req.PartnerCommission,
			}
			if partner.AutoPayment {
				t.Log = "PLANNED"
				t.Status = ubk_a2a.StatusPending
			} else {
				t.Log = "Will Be Paid Manually"
				t.Status = ubk_a2a.StatusManualPaid
			}
			err = a.repo.CreatePartnerA2ATransaction(ctx, t)
			if err != nil {
				a.log.Errorf("create partner payment: %v", err)
			}
		}
	}

	return
}

func (a *App) billingPayOrder(ctx context.Context, req model.PayOrderRequest, orderInfo model.OrderInfo) (driverBalance int, err error) {
	if req.PaymentTypeId == model.PaymentTypeCorp {
		driverBalance, err = a.repo.BillingPayOrderCorp(ctx, req, orderInfo)
	} else {
		if req.PromoCost <= 0 {
			driverBalance, err = a.repo.BillingPayOrderCash(ctx, req, orderInfo)
		} else {
			var amount, status int
			driverBalance, amount, status, err = a.repo.BillingPayOrderCashWithPromo(ctx, req, orderInfo)
			if err != nil {
				return
			}
			if status == model.PromocodeStatusSuccess && req.PromoCost != amount {
				status = model.PromocodeStatusCalculationError
			}
			err = a.repo.UpdatePromoDetails(ctx, req.OrderId, amount, int(req.PromoCost), status)
		}
	}

	return
}

func (a *App) orderPayCancelWithDebt(ctx context.Context, req model.PayOrderRequest, payToDriver bool) (err error) {
	err = a.repo.CreateDebt(ctx, req.ClientId, "client", req.OrderId, req.CardId, req.TotalClientCost)
	if err != nil {
		return
	}

	msgId := fmt.Sprintf("order:%d:debt", req.OrderId)

	// send notification to mytaxi operations channel

	msg := fmt.Sprintf(model.MessageToOperClientDebt, req.OrderId, req.TotalClientCost, req.ClientId, req.DriverId)

	a.repo.SendOperatorNotification("client_debts", msgId, msg)

	// send push notification to client

	a.repo.SendClientNotification(req.ClientId, req.OrderId, req.TotalClientCost, msgId, "client_debt_create")

	if payToDriver {
		r := model.A2CPaymentRequest{
			OrderId:  req.OrderId,
			DriverId: req.DriverId,
			Amount:   req.TotalClientCost,
			Reason:   model.PaymentReasonPayOrderForDriver,
			Comment:  req.Comment,
		}
		_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: r}, nil)
		if err != nil {
			return
		}
	}

	err = a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusCancelledWithDebt)

	return
}

func (a *App) isPayToDriver(ctx context.Context, req model.PayOrderRequest) (payToDriver bool, err error) {
	if req.TotalCost > a.repo.GetSysParam("max_client_debt_for_refill_driver_balance", "300000").Int() {

		reqId := fmt.Sprintf("pay_suspicious_order:%d", req.OrderId)
		msg := fmt.Sprintf(model.MessageToOperSuspiciousOrder, req.OrderId, req.TotalCost, req.ClientId, req.DriverId)
		a.repo.SendOperatorNotification("billing", reqId, msg)

		err = a.repo.UpdateOrderDriverPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuspiciousOrder)
		return
	}

	if req.TotalCost > 0 {
		payToDriver = true
	} else {
		err = a.repo.UpdateOrderDriverPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusEmptyCost)
	}

	return
}
