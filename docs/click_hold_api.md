# Click Hold API Documentation

## Overview

The Click Hold API allows you to create payment holds for orders using Click payment system. If the hold creation fails, the system automatically falls back to cash payment type.

## Endpoint

```
POST /v1/click/{order_id}/create-hold
```

## Request

### Path Parameters
- `order_id` (integer, required): The ID of the order for which to create a hold

### Request Body
```json
{
    "amount": 1000,
    "time_minutes": 60
}
```

### Request Fields
- `amount` (integer, required): Amount to hold in UZS (must be greater than 0)
- `time_minutes` (integer, optional): Hold duration in minutes (default: 60)

**Note:** C<PERSON>'s phone number is automatically retrieved from the database using the order's client_id.

## Response

### Success Response (Hold Created)
```json
{
    "status": "success",
    "message": "Hold created successfully",
    "payment_type": "click",
    "hold_id": "128815201",
    "payment_id": 4228551041,
    "payment_db_id": 123,
    "full_amount": 1000,
    "created_time": "2025-07-08T08:23:26.018422856"
}
```

### Success Response (Fallback to Cash)
```json
{
    "status": "success",
    "message": "Hold creation failed, payment type changed to cash",
    "payment_type": "cash"
}
```

### Error Response
```json
{
    "error": {
        "type": "validation_error",
        "message": "phone_number is required"
    }
}
```

## Response Fields

### Common Fields
- `status` (string): Always "success" for successful API calls
- `message` (string): Descriptive message about the operation
- `payment_type` (string): Either "click" or "cash"

### Click Payment Fields (when payment_type = "click")
- `hold_id` (string): Click's hold identifier
- `payment_id` (integer): Click's payment identifier
- `payment_db_id` (integer): Internal database payment record ID
- `full_amount` (integer): Amount that was held
- `created_time` (string): ISO timestamp when hold was created

## Behavior

1. **Order Validation**: The API first validates that the order exists
2. **Client Phone Retrieval**: Gets client's phone number from database using client_id
3. **Hold Creation**: Attempts to create a hold with Click payment system
3. **Success Path**: If hold is created successfully:
   - Creates a payment record in the database
   - Returns hold details
4. **Failure Path**: If hold creation fails:
   - Changes order payment type to cash using proper app service method
   - Publishes payment type change event to message broker
   - Returns success response with payment_type = "cash"
5. **Error Handling**: If payment record creation fails after successful hold:
   - Attempts to cancel the hold
   - Falls back to cash payment type with event publishing

## Example Usage

### cURL Example
```bash
curl -X POST "http://localhost:8080/v1/click/12345/create-hold" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 1000,
    "time_minutes": 60
  }'
```

### JavaScript Example
```javascript
const response = await fetch('/v1/click/12345/create-hold', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    amount: 1000,
    time_minutes: 60
  })
});

const result = await response.json();

if (result.payment_type === 'click') {
  console.log('Hold created:', result.hold_id);
} else if (result.payment_type === 'cash') {
  console.log('Fallback to cash payment');
}
```

## Error Codes

- `400 Bad Request`: Invalid request parameters
- `500 Internal Server Error`: Server-side error

## Notes

- The API is designed to be fault-tolerant - it will always try to ensure the order can be processed
- If Click payment fails, the system gracefully falls back to cash payment
- Hold duration is specified in minutes and defaults to 60 minutes
- Client phone numbers are automatically retrieved from the database using the order's client_id
- Amounts are specified in UZS (Uzbek Som)

## Related APIs

After creating a hold, you can use these Click client methods:
- `ConfirmHold(payment_id, amount)` - Confirm and charge the held amount
- `CancelHold(payment_id)` - Cancel the hold and release funds
- `GetHoldStatus(payment_id)` - Check the current status of the hold
