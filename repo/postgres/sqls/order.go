package sqls

const GetClientOrderPaymentStatus = `
SELECT client_payment_status FROM payments_orders WHERE id = $1;`

const GetDriverOrderPaymentStatus = `
SELECT driver_payment_status FROM payments_orders WHERE id = $1;`

const CreateOrderPaymentDetails = `
INSERT INTO payments_orders (id, corp_id, client_id, driver_id, payment_type_id)
VALUES ($1,NULLIF($2,0),$3,$4,$5)
ON CONFLICT (id) DO NOTHING;`

const UpdateOrderPaymentStatusToSuccess = `
UPDATE payments_orders SET client_payment_status = 1, driver_payment_status = 1
WHERE id = $1;`

const UpdateOrderClientPaymentStatus = `
UPDATE payments_orders SET client_payment_status = $2
WHERE id = $1;`

const UpdateOrderDriverPaymentStatus = `
UPDATE payments_orders SET driver_payment_status = $2
WHERE id = $1;`

const ChangeOrderPaymentType = `
UPDATE payments_orders SET payment_type_id = $2
WHERE id = $1;`

const GetOrderTipsByDriverId = `
SELECT
	p.order_id,
	p.amount,
	p.created_at::text
FROM payments p
JOIN payments_orders o ON o.id = p.order_id
WHERE o.driver_id = $1 AND p.reason = 8 AND p.status = 2
ORDER BY p.id DESC;`

const GetOrderTipsByOrderId = `
SELECT
	order_id,
	amount,
	created_at::text
FROM payments
WHERE order_id = $1 AND reason = 8 AND status = 2
LIMIT 1;`
