package model

import (
	"errors"
	"time"

	null "github.com/guregu/null/v6"
)

const (
	OrderStatusAcceptedByDriver = 2
	OrderStatusDriverArrived    = 3
	OrderStatusClientOnBoard    = 4

	ChangedPaymentType = "changed_payment_type"
)

type OrderStatusEvent struct {
	OrderId  int    `json:"order_id"`
	ClientId int    `json:"client_id"`
	DriverId int    `json:"driver_id"`
	Status   string `json:"status"`
}

type FinishedOrderEvent struct {
	OrderId           int       `json:"order_id"`
	CorpId            int       `json:"corp_id"`
	ClientId          int       `json:"client_id"`
	DriverId          int       `json:"driver_id"`
	TariffId          int       `json:"tariff_id"`
	InitiatorId       int       `json:"initiator_id"`
	PaymentTypeId     int       `json:"payment_type_id"`
	Commission        float32   `json:"commission"`
	PartnerCommission float32   `json:"partner_commission"`
	CreateTime        time.Time `json:"create_time"`
	EndTime           time.Time `json:"end_time"`
	Cost              Cost      `json:"cost"`
}

type Cost struct {
	Minimal        int `json:"minimal"`
	Surge          int `json:"surge"`
	Total          int `json:"total_cost"`
	TotalClient    int `json:"total"`
	AirConditioner int `json:"air_conditioner"`
	DoorToDoor     int `json:"door_to_door"`
	Promo          int `json:"promo"`
	PaidFromBonus  int `json:"paid_from_bonus"`
	Discount       int `json:"discount"`
	TariffDiscount int `json:"tariff_discount"`
}

type PayOrderRequest struct {
	OrderId            int     `json:"order_id"`
	ClientId           int     `json:"client_id"`
	DriverId           int     `json:"driver_id"`
	InitiatorId        int     `json:"initiator_id"`
	PaymentTypeId      int     `json:"payment_type_id"`
	CardId             int     `json:"card_id,omitempty"`
	CorpId             int     `json:"company_id,omitempty"`
	TotalCost          int     `json:"total_cost,omitempty"`
	TotalClientCost    int     `json:"total_client_cost,omitempty"`
	PromoCost          int     `json:"promo_cost,omitempty"`
	DiscountCost       int     `json:"discount_cost,omitempty"`
	TariffDiscountCost int     `json:"tariff_discount_cost,omitempty"`
	CashbackCost       int     `json:"cashback_cost,omitempty"`
	Commission         float32 `json:"commission,omitempty"`
	PartnerCommission  float32 `json:"partner_commission,omitempty"`
	Comment            string  `json:"comment,omitempty"`
	Amount             int     `json:"amount,omitempty"`
	PayToDriverCard    bool    `json:"pay_to_driver_card,omitempty"`
}

func (r *PayOrderRequest) CheckRequest() error {
	if r.OrderId == 0 {
		return errors.New("empty order_id")
	}
	if r.ClientId == 0 {
		return errors.New("empty client_id")
	}
	if r.DriverId == 0 {
		return errors.New("empty driver_id")
	}
	if r.PaymentTypeId == 0 {
		return errors.New("empty payment_type_id")
	}
	if r.TotalCost == 0 {
		return errors.New("empty total_cost")
	}
	if r.PaymentTypeId == PaymentTypeCorp && r.CorpId == 0 {
		return errors.New("empty corp_id")
	}

	return nil
}

type OrderInfo struct {
	ClientId          int         `db:"client_id"`
	DriverId          int         `db:"driver_id"`
	OrderFrom         int         `db:"orderFrom"`
	FromAdrs          string      `db:"from_adres"`
	ToAdrs            string      `db:"to_adres"`
	Date              string      `db:"date"`
	TarifName         string      `db:"tarif_name"`
	ClientSatTime     null.String `db:"client_sat_time"`
	DrArrivedTime     null.String `db:"dr_arrived_time"`
	EndTime           null.String `db:"end_time"`
	Promocode         null.String `db:"promocode"`
	CarName           null.String `db:"name"`
	CarNumber         null.String `db:"number"`
	CarColorNameRu    null.String `db:"name_ru"`
	ClientCardId      null.Int    `db:"card_id"`
	DistanceToClient  null.Int    `db:"distance_to_client"`
	DistanceBeforeSat null.Int    `db:"distance_before_client_sat"`
	DrivingDistance   null.Int    `db:"driving_distance"`
	Minimalka         null.Int    `db:"minimalka"`
	VkMin             null.Int    `db:"vk_min"`
	OjidaniyMinut     null.Int    `db:"ojidaniy_minut"`
	Vgorode           null.Int    `db:"vgorode"`
	VkOjidaniy        null.Int    `db:"vk_ojidaniy"`
	WaitingTime       null.Int    `db:"waiting_time"`
	PaidFromBonus     null.Int    `db:"paid_from_bonus"`
	TotalClientCost   null.Int    `db:"total_client_cost"`
	InterestRate      null.Float  `db:"interest_rate"`
	CoordinatesAX     float64     `db:"coordinates_a_x"`
	CoordinatesAY     float64     `db:"coordinates_a_y"`
	CoordinatesBX     float64     `db:"coordinates_b_x"`
	CoordinatesBY     float64     `db:"coordinates_b_y"`
}

type SuspiciousOrderInfo struct {
	Id              int      `db:"id"`
	ClientId        int      `db:"client_id"`
	DriverId        int      `db:"driver_id"`
	TotalClientCost null.Int `db:"total_client_cost"`
	PaymentStatus   null.Int `db:"driver_payment_status_id"`
}

type OrderTips struct {
	OrderId     int    `json:"order_id"`
	Amount      int    `json:"amount"`
	CreatedAt   string `json:"created_at"`
	TariffName  string `json:"tariff_name"`
	TariffColor string `json:"tariff_color"`
}
