package model

import (
	null "github.com/guregu/null/v6"
)

const (
	// payment statuses
	PaymentStatusCreated              = 1
	PaymentStatusFinished             = 2
	PaymentStatusPending              = 3
	PaymentStatusCancelled            = -1
	PaymentStatusCancelledAfterFinish = -2

	// order payment statuses
	OrderPaymentStatusSuccess           = 1
	OrderPaymentStatusSuccessToBalance  = 2
	OrderPaymentStatusEmptyCost         = 3
	OrderPaymentStatusCancelledWithDebt = 4
	OrderPaymentStatusSuspiciousOrder   = 7

	// payment reasons
	PaymentReasonPayOrder                  = 6
	PaymentReasonPayOrderForDriver         = 7
	PaymentReasonPayTips                   = 8
	PaymentReasonPayOrderDiscountForDriver = 9
	PaymentReasonPayDebt                   = 10
	PaymentReasonRefillBalance             = 11
	PaymentReasonAutorefillBalance         = 12
	PaymentReasonPayOrderCancel            = 14
	PaymentReasonDriverBonus               = 15

	// provider types
	ProviderIdPayme  = 1
	ProviderIdUbk    = 2
	ProviderIdPaynet = 4
	ProviderIdClick  = 5
	ProviderIdAtmos  = 6

	// payment types
	PaymentTypeCash  = 1
	PaymentTypeCorp  = 2
	PaymentTypePayme = 3
	PaymentTypeClick = 4
	PaymentTypeAtmos = 5

	// TransactionTimeout = 60 * 60 * 12 // 12 hours
)

type Payment struct {
	Id         int      `json:"id"`
	UserId     int      `json:"user_id"`
	CardId     null.Int `json:"card_id"`
	OrderId    null.Int `json:"order_id"`
	Amount     int      `json:"amount"`
	Status     int      `json:"status"`
	Reason     int      `json:"reason"`
	ProviderId int      `json:"provider_id"`
	Invoice    string   `json:"invoice"`
	UserType   string   `json:"user_type"`
	IsP2p      bool     `json:"is_p2p"`
}

func (p Payment) IsFinished() bool {
	return p.Status == PaymentStatusFinished
}

func (p Payment) IsCreated() bool {
	return p.Status == PaymentStatusCreated || p.Status == PaymentStatusPending
}

func (p Payment) IsCanceled() bool {
	return p.Status == PaymentStatusCancelled || p.Status == PaymentStatusCancelledAfterFinish
}

type PaymentDetails struct {
	Id         int      `json:"id"`
	UserId     int      `json:"user_id"`
	CardId     null.Int `json:"card_id"`
	OrderId    null.Int `json:"order_id"`
	Amount     int      `json:"amount"`
	Status     int      `json:"status"`
	Reason     int      `json:"reason"`
	ProviderId int      `json:"provider_id"`
	Invoice    string   `json:"invoice"`
	UserType   string   `json:"user_type"`
	IsP2p      bool     `json:"is_p2p"`
	PaymeToken string   `json:"-"`
	ClienId    null.Int `json:"-"`
	DriverId   null.Int `json:"-"`
}

type A2CPaymentRequest struct {
	OrderId    int    `json:"order_id"`
	DriverId   int    `json:"driver_id"`
	CardId     int    `json:"card_id,omitempty"`
	Amount     int    `json:"amount"`
	Reason     int    `json:"reason_id,omitempty"`
	CardNumber string `json:"card_number,omitempty"`
	Comment    string `json:"comment,omitempty"`
}

type GeneratePaymentLinkRequest struct {
	Amount        int    `json:"amount"`
	TransactionID string `json:"transaction_id"`
	Auth          bool   `json:"auth"`
	HideCross     bool   `json:"hide_cross"`
	ReturnPage    string `json:"return_page"`
}
